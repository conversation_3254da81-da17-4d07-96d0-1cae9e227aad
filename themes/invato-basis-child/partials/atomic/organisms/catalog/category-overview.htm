[hhgCategoryDetail]
slug = "{{ :slug }}"
==
{% import 'macros/prices' as format %}

<div class="">
    <div class="lg:grid lg:grid-cols-12 lg:gap-8">
        <div class="hidden lg:block lg:col-span-3 xl:col-span-4"></div>
        <div class="relative flex items-center justify-between lg:col-span-9 xl:col-span-8">
            <div class="w-4/5 md:w-full">
                <h1 class="font-bold mb-0">{{ category.title }}</h1>
            </div>
            <div class="lg:hidden flex justify-end" x-data="{ open: false }">

                <div class="absolute top-0 -right-4 z-30">

                        <div class="bg-primary-600 text-white text-sm px-4 py-2.5" @click="open = !(open)">
                            <i class="fa-solid fa-chevrons-left pr-2"></i>{{ 'Cat.'|_ }}
                        </div>

                        <div class="" x-show="open" x-cloak>
                            <div class="" @click.outside="open = false">
                                <div class="overflow-y-auto h-[410px]">
                                    {% partial 'atomic/molecules/catalog/categories-pop_out' %}
                                </div>

                            </div>
                        </div>

                </div>
            </div>
        </div>
    </div>
    <div class="lg:grid lg:grid-cols-12 lg:gap-8">
        <div class="hidden lg:block lg:col-span-3 space-y-8">
            <div class="">
                {% partial 'atomic/molecules/catalog/categories' %}
            </div>
            <div class="flex justify-end">
                <a href="{{ catalogPage|link }}" class="inline-flex items-center hover:text-[#3A8F21] hover:underline ">
                    <i class="fa-solid fa-circle-chevron-left text-[#3A8F21] text-2xl pr-1.5"></i>
                    {{ 'Terug naar het overzicht'|_ }}
                </a>
            </div>
        </div>
        <div class="hidden xl:block xl:col-span-1"></div>
        <div class="space-y-16 lg:col-span-9 xl:col-span-8 mt-12">
            {% if category.children.toArray() %}
                <div class="grid grid-cols-2 gap-4 lg:grid-cols-3 md:gap-8">
                    {% for child in category.children %}
                        <div class="catalog-category">
                            {% partial 'atomic/molecules/catalog/category' item=child categoryPage=categoryPage %}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            {% if category.description %}
                <div class="">
                    <div class="content_section">
                        {{ category.description|content }}
                    </div>
                </div>
            {% endif %}

            <div class="flex items-center justify-between lg:justify-end border-b border-gray-200 pb-2">
                <div class="lg:hidden flex items-center">
                    <a href="{{ catalogPage|link }}" class="inline-flex items-center hover:text-[#3A8F21] hover:underline ">
                        <i class="fa-solid fa-circle-chevron-left text-[#3A8F21] text-2xl pr-1.5"></i>
                        {{ 'Terug naar het overzicht'|_ }}
                    </a>
                </div>
                <div class="flex items-center" x-data="{ open: false, sorting: 'popular' }">
                    <div class="relative inline-block text-left" @click.away="open = false">
                        <div>
                            <button type="button" class="group inline-flex justify-center font-medium text-gray-700 hover:text-gray-900" id="menu-button" aria-expanded="false" aria-haspopup="true" @click="open = !(open)">
                                {{ 'Sorteren'|_ }}
                                <svg class="-mr-1 ml-1 h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>

                        <div
                            class="absolute right-0 z-10 mt-2 w-40 origin-top-right rounded-md bg-white shadow-2xl ring-1 ring-black ring-opacity-5 focus:outline-none"
                            role="menu"
                            aria-orientation="vertical"
                            aria-labelledby="menu-button"
                            tabindex="-1"
                            x-show="open"
                            x-transition:enter="transition ease-out duration-100"
                            x-transition:enter-start="transform opacity-0 scale-95"
                            x-transition:enter-end="transform opacity-100 scale-100"
                            x-transition:leave="transition ease-in duration-75"
                            x-transition:leave-start="transform opacity-100 scale-100"
                            x-transition:leave-end="transform opacity-0 scale-95"
                            x-cloak
                        >
                            <div class="py-1" role="none">
                                <label class="block px-4 py-2 hover:bg-gray-100" role="menuitem" tabindex="-1" id="menu-item-2" :class="{ 'font-medium text-gray-900': sorting === 'newest', 'text-gray-500': sorting !== 'newest' }">
                                    <input type="radio" name="sorting" id="sorting1" value="newest" x-model="sorting" class="sr-only" data-request="hhgCategoryDetail::onChangeSorting" data-request-update="{ 'atomic/molecules/catalog/product-list': '#productlist' }">
                                    {{ 'Nieuwste'|_ }}
                                </label>
                                <label class="block px-4 py-2 hover:bg-gray-100" role="menuitem" tabindex="-1" id="menu-item-3" :class="{ 'font-medium text-gray-900': sorting === 'pricelow', 'text-gray-500': sorting !== 'pricelow' }">
                                    <input type="radio" name="sorting" id="sorting2" value="pricelow" x-model="sorting" class="sr-only" data-request="hhgCategoryDetail::onChangeSorting" data-request-update="{ 'atomic/molecules/catalog/product-list': '#productlist' }">
                                    {{ 'Prijs laag - hoog'|_ }}
                                </label>
                                <label class="block px-4 py-2 hover:bg-gray-100" role="menuitem" tabindex="-1" id="menu-item-4" :class="{ 'font-medium text-gray-900': sorting === 'pricehigh', 'text-gray-500': sorting !== 'pricehigh' }">
                                    <input type="radio" name="sorting" id="sorting3" value="pricehigh" x-model="sorting" class="sr-only" data-request="hhgCategoryDetail::onChangeSorting" data-request-update="{ 'atomic/molecules/catalog/product-list': '#productlist' }">
                                    {{ 'Prijs hoog - laag'|_ }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            {% if category.products %}
                <div class="grid sm:grid-cols-2 gap-4 lg:grid-cols-3 md:gap-8" id="productlist">
                    {% partial 'atomic/molecules/catalog/product-list' %}
                </div>
            {% endif %}

        </div>
    </div>
</div>
