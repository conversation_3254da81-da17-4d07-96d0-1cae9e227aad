url = "/catalogus/categorie/:slug"
layout = "default"
title = "Categorie"
==

<section
    data-name="catalog-category"
    data-category="catalog"
    class="relative py-8">

    <div class="container space-y-8 lg:space-y-12">
        <div class="space-y-8">
            <nav class="flex dark:border-gray-600" aria-label="Breadcrumb" id="breadcrumbs">
                <ol role="list" class="flex items-center w-full space-x-2">
                    <li class="flex items-center">
                        <a href="/" class="text-gray-400 hover:text-gray-600 text-sm">
                            <i class="fa-solid fa-house"></i>
                            <span class="sr-only">Home</span>
                        </a>
                    </li>

                    <li class="flex items-center">
                        <i class="fa-regular fa-chevron-right text-gray-400 text-xs"></i>
                        <a href="{{ catalogPage | link }}"
                           class="ml-2 text-xs font-medium text-gray-400 hover:text-gray-700">{{ 'Catalogus'|_ }}</a>
                    </li>

                    {% for item in category.getParents() %}
                        <li class="flex items-center">
                            <i class="fa-regular fa-chevron-right text-gray-400 text-xs"></i>
                            <a href="{{ categoryPage | page({ slug: item.slug }) }}"
                                class="ml-2 text-xs font-medium text-gray-400 hover:text-gray-700">{{ item.title }}</a>
                        </li>
                    {% endfor %}

                    <li class="flex items-center">
                        <i class="fa-regular fa-chevron-right text-gray-400 text-xs"></i>
                        <span class="ml-2 text-xs font-medium text-gray-500" aria-current="page">{{ category.title }}</span>
                    </li>

                </ol>
            </nav>
        </div>

        <div class="md:pb-8 xl:pb-16">
            {% ajaxPartial 'atomic/organisms/catalog/category-overview' %}
        </div>

    </div>

</section>
