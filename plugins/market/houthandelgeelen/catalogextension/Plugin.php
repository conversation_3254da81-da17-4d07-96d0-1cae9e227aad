<?php namespace Market\HouthandelGeelen\CatalogExtension;

use System\Classes\PluginBase;
use Market\HouthandelGeelen\CatalogExtension\Components\HhgCategoryDetail;
use Market\HouthandelGeelen\CatalogExtension\Components\HhgProductList;

/**
 * Plugin class for Houthandel Geelen Catalog Extension
 */
class Plugin extends PluginBase
{
    /**
     * Returns information about this plugin.
     */
    public function pluginDetails()
    {
        return [
            'name' => 'Houthandel Geelen Catalog Extension',
            'description' => 'Extends the catalog plugin with custom filtering functionality',
            'author' => 'Market',
            'icon' => 'icon-shopping-cart'
        ];
    }

    /**
     * Register components
     */
    public function registerComponents()
    {
        return [
            HhgCategoryDetail::class => 'hhgCategoryDetail',
            HhgProductList::class => 'hhgProductList',
        ];
    }

    /**
     * Boot method
     */
    public function boot()
    {
        // Plugin boot logic
    }
}
