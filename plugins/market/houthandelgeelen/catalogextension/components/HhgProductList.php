<?php namespace Market\HouthandelGeelen\CatalogExtension\Components;

use Cms\Classes\ComponentBase;
use Invato\Catalog\Models\Category;
use Invato\Catalog\Models\Product;
use Invato\Catalog\Models\CatalogSettings;

/**
 * HhgProductList Component
 */
class HhgProductList extends ComponentBase
{
    public $products;
    public $categories;
    public $categoryPage;
    public $productPage;
    public $catalogPage;

    public function componentDetails()
    {
        return [
            'name' => 'HHG Product List',
            'description' => 'Displays product list with custom filtering'
        ];
    }

    public function defineProperties()
    {
        return [];
    }

    public function onRun()
    {
        $this->categoryPage = $this->page['categoryPage'] = CatalogSettings::get('categoryPage');
        $this->productPage = $this->page['productPage'] = CatalogSettings::get('productPage');
        $this->catalogPage = $this->page['catalogPage'] = CatalogSettings::get('catalogPage');
        $this->products = $this->page['products'] = Product::all();
        $this->categories = $this->page['categories'] = Category::all();
    }

    public function onChangeSorting()
    {
        $sortBy = input('sorting');
        $query = Product::withoutTrashed();

        if ($sortBy == 'newest') {
            $query->orderBy('id', 'desc');
        } elseif ($sortBy == 'pricelow') {
            $query->orderBy('price', 'asc');
        } elseif ($sortBy == 'pricehigh') {
            $query->orderBy('price', 'desc');
        } else {
            $query->orderBy('sort_order');
        }

        $this->products = $this->page['products'] = $query->get();
    }
}
