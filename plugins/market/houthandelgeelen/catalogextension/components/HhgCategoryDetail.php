<?php namespace Market\HouthandelGeelen\CatalogExtension\Components;

use Cms\Classes\ComponentBase;
use Invato\Catalog\Models\Category;
use Invato\Catalog\Models\Product;
use Invato\Catalog\Models\CatalogSettings;

/**
 * HhgCategoryDetail Component
 */
class HhgCategoryDetail extends ComponentBase
{
    public $category;
    public $products;
    public $categoryPage;
    public $productPage;
    public $catalogPage;

    public function componentDetails()
    {
        return [
            'name' => 'HHG Category Detail',
            'description' => 'Displays category details with custom filtering'
        ];
    }

    public function defineProperties()
    {
        return [
            'slug' => [
                'title' => 'Category slug',
                'description' => 'Enter category slug',
                'default' => '{{ :slug }}',
                'type' => 'string',
            ],
        ];
    }

    public function onRun()
    {
        $this->categoryPage = $this->page['categoryPage'] = CatalogSettings::get('categoryPage');
        $this->productPage = $this->page['productPage'] = CatalogSettings::get('productPage');
        $this->catalogPage = $this->page['catalogPage'] = CatalogSettings::get('catalogPage');
        $this->category = $this->page['category'] = $this->getCategory();
        $this->products = $this->page['products'] = $this->getProducts();
    }

    protected function getCategory()
    {
        $slug = $this->property('slug');
        $category = new Category;
        $query = $category->query();
        $query->where('slug', $slug);
        $category = $query->first();

        return $category;
    }

    protected function getProducts()
    {
        if (!$this->category) {
            return collect();
        }

        $query = $this->category->products();
        
        // Apply sorting
        $sortBy = input('sorting', 'newest');
        if ($sortBy == 'newest') {
            $query->orderBy('id', 'desc');
        } elseif ($sortBy == 'pricelow') {
            $query->orderBy('price', 'asc');
        } elseif ($sortBy == 'pricehigh') {
            $query->orderBy('price', 'desc');
        } else {
            $query->orderBy('sort_order');
        }

        // Apply kopmaat filter
        $kopmaat = input('kopmaat_filter');
        if ($kopmaat) {
            $query->whereJsonContains('custom_options->kopmaat', $kopmaat);
        }

        // Apply lengte filter
        $lengte = input('lengte_filter');
        if ($lengte) {
            $query->whereJsonContains('custom_options->lengte', $lengte);
        }

        return $query->get();
    }

    public function onChangeSorting()
    {
        $this->products = $this->page['products'] = $this->getProducts();
    }

    public function onChangeKopmaat()
    {
        $this->products = $this->page['products'] = $this->getProducts();
    }

    public function onChangeLengte()
    {
        $this->products = $this->page['products'] = $this->getProducts();
    }
}
